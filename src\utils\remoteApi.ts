import logger from "./logger";

export class RemoteApiClient {
  private companyId: number;
  private apiKey: string;
  private remoteApiUrl: string;

  constructor() {
    this.companyId = parseInt(process.env.LOCAL_COMPANY_ID || "0");
    this.apiKey = process.env.REMOTE_API_KEY || "";
    this.remoteApiUrl = "https://talisiapos.duckdns.org/graphql";
  }

  isConfigured(): boolean {
    return Boolean(this.remoteApiUrl && this.apiKey && this.companyId);
  }

  async getChanges(entityName: string, lastSync: Date) {
    try {
      const response = await fetch(
        `${this.remoteApiUrl}/sync/${entityName}/changes`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${this.apiKey}`,
          },
          body: JSON.stringify({
            companyId: this.companyId,
            lastSync: lastSync.toISOString(),
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`Remote API error: ${response.statusText}`);
      }

      const data = await response.json();
      return data.changes;
    } catch (error) {
      logger.error(
        `Failed to fetch changes from remote for ${entityName}:`,
        error
      );
      throw error;
    }
  }

  async pushChanges(entityName: string, changes: any[]) {
    try {
      const response = await fetch(
        `${this.remoteApiUrl}/sync/${entityName}/push`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${this.apiKey}`,
          },
          body: JSON.stringify({
            companyId: this.companyId,
            changes,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`Remote API error: ${response.statusText}`);
      }

      const data = await response.json();
      return data.success;
    } catch (error) {
      logger.error(
        `Failed to push changes to remote for ${entityName}:`,
        error
      );
      throw error;
    }
  }
}
