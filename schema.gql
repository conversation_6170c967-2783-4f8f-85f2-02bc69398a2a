# -----------------------------------------------
# !!! THIS FILE WAS GENERATED BY TYPE-GRAPHQL !!!
# !!!   DO NOT MODIFY THIS FILE BY YOURSELF   !!!
# -----------------------------------------------

type Approval {
  approvalDate: DateTime
  approver: Employee
  approverId: Float
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  feature: String!
  id: Float!
  requestId: Float!
  requester: Employee
  requesterId: Float
  status: Boolean!
  type: String!
  updatedAt: String!
}

type BatchStock {
  batch: String!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  expireDate: String!
  id: Float!
  item: Item!
  itemId: Float!
  stock: Float!
  storeItemStocks: [StoreItemStock!]
  updatedAt: String!
}

type Bill {
  amount: Float!
  cleared: Boolean!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  id: Float!
  inventoryId: Float!
  inventoryTransfer: Inventory!
  paymentType: String!
  updatedAt: String!
}

"""Billing cycle for payment"""
enum BillingCycle {
  ANNUALLY
  MONTHLY
  QUARTERLY
}

type BooleanResponse {
  error: FieldError
  status: Boolean!
}

type BooleanResponseId {
  error: FieldError
  id: Float
  name: String
  status: Boolean!
}

type BooleanResponseWithType {
  data: Type
  error: FieldError
  status: Boolean!
}

input BulkItemInput {
  items: [ItemInput!]!
}

type Category {
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  id: Float!
  name: String!
  parentCategoryId: Float
  type: Type!
  typeId: Float!
  updatedAt: String!
  user: [User!]
}

input CategoryArgs {
  name: String!
  type: Float!
}

type CategoryResponse {
  category: Category
  error: FieldError
}

input CategoryTypeArgs {
  name: String!
  typeName: String!
}

type Company {
  branches: [Float!]!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  departments: [Department!]!
  email: String!
  employees: [Employee!]!
  features: [Feature!]
  id: Float!
  isBranch: Boolean!
  isParent: Boolean!
  location: String!
  logo: String!
  name: String!
  parentId: Float!
  payments: [Payment!]
  phone: String!
  poBox: String!
  registrationNumber: String!
  syncHistory: [SyncHistory!]
  syncUrl: String
  tinNumber: String!
  type: String!
  updatedAt: String!
  users: [User!]
  website: String!
}

input CreatePaymentInput {
  amount: Float!
  autoRenew: Boolean
  billingCycle: String!
  endDate: DateTime!
  features: String
  maxUsers: Float
  packageName: String!
  paymentReference: String
  startDate: DateTime!
  status: String!
}

"""
The javascript `Date` as string. Type represents date and time as the ISO Date string.
"""
scalar DateTime

type Department {
  company: Company!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  description: String!
  employees: [Employee!]
  headOfDepartment: Employee
  id: Float!
  name: String!
  parentId: Float
  status: String
  type: String
  updatedAt: String!
}

input DepartmentInputArgs {
  description: String
  headOfDepartmentId: Float
  name: String!
  parentId: Float
  status: String
  type: String
}

input DispatchInput {
  batch: String
  itemId: Float!
  locationId: Float!
  quantity: Float!
  remarks: String
  unit: String!
}

input EditUserArgs {
  email: String!
  firstname: String!
  image: String!
  lastname: String!
  middlename: String!
  phone: String!
}

input EmailPasswordArgs {
  email: String!
  password: String!
}

type Employee {
  approvedApprovals: [Approval!]
  approved_stock: [Inventory!]
  authorizedExpenses: [Expense!]
  company: Company!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  department: Department
  departmentId: Float
  designation: String!
  headingDepartment: Department
  headingDepartmentId: Float
  id: Float!
  image: String!
  licenceNumber: String!
  received_stock: [Inventory!]
  requestedApprovals: [Approval!]
  requestedExpenses: [Expense!]
  role: Role!
  roleId: Float!
  served_stock: [Inventory!]
  status: String!
  store: Store
  storeId: Float
  updatedAt: String!
  user: User!
  userId: Float!
}

type Expense {
  amount: Float!
  assetId: Float!
  assetType: String!
  authorizer: Employee
  authorizerId: Float!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  details: String!
  expenseDate: String!
  id: Float!
  requester: Employee
  requesterId: Float!
  status: String!
  title: String!
  type: String!
  updatedAt: String!
}

input ExpenseFilterInput {
  endDate: String
  startDate: String
}

input ExpenseInput {
  amount: Float!
  assetId: Float
  assetType: String
  details: String!
  expenseDate: String!
  title: String!
  type: String!
}

type Feature {
  companies: [Company!]
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  id: Float!
  name: String!
  updatedAt: String!
}

type FieldError {
  message: String!
  target: String!
}

type Import {
  batch: String!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  expireDate: String!
  id: Float!
  importDate: String!
  importPrice: Float!
  item: Item!
  itemId: Float!
  quantity: Float!
  receipt: String!
  sellingPrice: Float!
  supplier: String!
  unit: String!
  updatedAt: String!
}

input ImportInput {
  batch: String
  expireDate: String
  importDate: String!
  importPrice: Float!
  itemId: Float!
  pieceSellingPrice: Float
  quantity: Float!
  receipt: String
  sellingPrice: Float
  subPieceSellingPrice: Float
  supplier: String!
  unit: String!
}

type Inventory {
  approver: Employee
  approverId: Float
  bill: Bill
  companyId: Float!
  consumer: Employee
  consumerId: Float
  createdAt: String!
  customerTag: String
  deleted: Boolean!
  destinationStore: Store
  destinationStoreId: Float
  details: String
  dispatched: Boolean!
  granted: Boolean!
  id: Float!
  items: [Item!]!
  keeper: Employee
  keeperId: Float
  received: Boolean!
  returnDate: String
  sourceStore: Store
  sourceStoreId: Float
  startDate: String
  transferDate: String!
  transfers: [Transfer!]!
  type: String!
  updatedAt: String!
}

type Item {
  barcode: String
  batchStocks: [BatchStock!]
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  description: String!
  id: Float!
  image: String!
  imports: [Import!]
  internal: Boolean!
  inventoryTransfers: [Inventory!]
  name: String!
  reference: String!
  reorder: Float!
  sellingPrice: Float!
  stock: Float!
  storeItemStocks: [StoreItemStock!]
  transfers: [Transfer!]
  type: String!
  unit: String!
  units: [Unit!]!
  updatedAt: String!
}

input ItemInput {
  description: String
  image: String
  internal: Boolean
  name: String!
  reference: String
  reorder: Float!
  sellingPrice: Float
  type: String!
  unit: String!
}

type LogEntry {
  action: String
  companyId: Int
  errorCode: String
  level: String!
  message: String!
  severity: String
  stackTrace: String
  timestamp: String!
  userId: Int
}

type Message {
  attended: Boolean!
  createdAt: String!
  id: Float!
  message: String!
  senderEmail: String!
  senderName: String!
  senderPhone: String
  subject: String!
  updatedAt: String!
}

input MessageInput {
  message: String!
  senderEmail: String!
  senderName: String!
  senderPhone: String
  subject: String!
}

type MessageResponse {
  errors: [FieldError!]
  message: Message
  status: Boolean!
}

type Mutation {
  addCategory(args: CategoryArgs!): BooleanResponse!
  addCategoryWithTypeName(args: CategoryTypeArgs!): CategoryResponse!
  addCompanyWithAddress(params: RegisterCompanyAddressedArgs!): BooleanResponse!
  addDepartment(params: DepartmentInputArgs!): BooleanResponse!
  addExpense(args: ExpenseInput!): BooleanResponse!
  addFeature(companyId: Float!, name: String!): BooleanResponse!
  addItem(args: ItemInput!): BooleanResponse!
  addItemsFromExcel(args: BulkItemInput!): BooleanResponse!
  addPermission(name: String!, roleId: Float, userId: Float): PermissionResponse!
  addRole(name: String!): BooleanResponse!
  addService(args: ServiceInput!): BooleanResponse!
  addStore(args: StoreInput!): BooleanResponse!
  addType(args: TypeArgs!): BooleanResponseWithType!
  addUnit(args: UnitInput!): BooleanResponse!
  assignStoreKeeper(storeId: Float!, userId: Float!): BooleanResponse!
  authorizeExpense(id: Float!): BooleanResponse!
  cancelPayment(id: Float!, reason: String): BooleanResponse!
  changeEmployeeRole(companyRole: Float!, departmentId: Float!, designation: String!, employeeId: Float!): BooleanResponse!
  changeEmployeeStatus(employeeId: Float!, status: String!): BooleanResponse!
  changeInventoryApprovalStatus(inventoryId: Float!): BooleanResponse!
  changeInventoryDispatchedStatus(inventoryId: Float!): BooleanResponse!
  changeInventoryReceivedStatus(inventoryId: Float!): BooleanResponse!
  changeInventorySoldStatus(inventoryId: Float!): BooleanResponse!
  changePassword(currentPassword: String!, newPassword: String!): BooleanResponse!
  changePaymentStatus(id: Float!, status: String!): BooleanResponse!
  clearBill(saleId: Float!): BooleanResponse!
  clearServedBill(inventoryId: Float!): BooleanResponse!
  createPayment(input: CreatePaymentInput!): Payment!
  deleteBillItem(inventoryId: Float!, transferId: Float!): BooleanResponse!
  deleteCategory(id: Float!): BooleanResponse!
  deleteExpense(id: Float!): BooleanResponse!
  deleteFeature(id: Float!): BooleanResponse!
  deleteItem(id: Float!): BooleanResponse!
  deletePayment(id: Float!): BooleanResponse!
  deletePermission(id: Float!): BooleanResponse!
  deleteRole(id: Float!): BooleanResponse!
  deleteType(id: Float!): BooleanResponse!
  deleteUnit(id: Float!): BooleanResponse!
  dispatchItems(args: [DispatchInput!]!): BooleanResponse!
  editBillItem(inventoryId: Float!, newQuantity: Float!, transferId: Float!): TransferResponse!
  editCategory(args: CategoryArgs!, id: Float!): BooleanResponse!
  editCategoryByName(args: CategoryArgs!, name: String!): BooleanResponse!
  editDepartment(id: Float!, params: DepartmentInputArgs!): BooleanResponse!
  editExpense(args: ExpenseInput!, id: Float!): BooleanResponse!
  editFeature(id: Float!, name: String!): BooleanResponse!
  editItem(args: ItemInput!, id: Float!): BooleanResponse!
  editPermission(id: Float!, name: String!): BooleanResponse!
  editRole(args: RoleArgs!, id: Float!): BooleanResponse!
  editService(args: ServiceInput!, id: Float!): BooleanResponse!
  editStore(args: StoreEditInput!): BooleanResponse!
  editType(args: TypeEditArgs!, id: Float!): BooleanResponse!
  editUser(id: Float!, params: EditUserArgs!): BooleanResponse!
  forgotPassword(email: String!): BooleanResponse!
  importItem(args: ImportInput!): BooleanResponse!
  instantTransfer(args: [TransferInput!]!, destinationStore: Float!, sourceStore: Float!): BooleanResponse!
  login(params: EmailPasswordArgs!): UserResponse!
  logout: Boolean!
  manageUserPermissions(id: Float!, permissions: [Float!]!): BooleanResponse!
  quickSale(args: [SaleInput!]!): BooleanResponse!
  receiveMessage(input: MessageInput!): MessageResponse!
  register(params: RegisterUserArgs!): BooleanResponse!
  registerCompany(params: RegisterCompanyArgs!): BooleanResponseId!
  registerEmployee(params: RegisterEmployeeArgs!): BooleanResponse!
  removePermission(name: String!, roleId: Float, userId: Float): BooleanResponse!
  resetPassword(newPassword: String!, token: String!): UserResponse!
  servePayLater(args: [SaleInput!]!, customerTag: String, servedTo: Float): BooleanResponse!
  servePendingOrder(transferId: Float!): BooleanResponse!
  setHeadOfDepartment(departmentId: Int!, employeeId: Int!): BooleanResponse!
  transferItems(args: [DispatchInput!]!): BooleanResponse!
  triggerSync(companyId: Float!): BooleanResponse!
  updateBill(args: [SaleInput!]!, inventoryId: Float!): BooleanResponse!
  updateMessageStatus(attended: Boolean!, messageId: Float!): MessageResponse!
  updatePayment(id: Float!, input: UpdatePaymentInput!): Payment
  updateSyncConfig(config: SyncConfigInput!): BooleanResponse!
  updateUnit(args: UnitInput!, id: Float!): BooleanResponse!
  writeOffItems(args: [WriteOffInput!]!): BooleanResponse!
}

type Payment {
  amount: Float!
  autoRenew: Boolean!
  billingCycle: BillingCycle!
  cancellationReason: String
  company: Company!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  endDate: String!
  features: String
  id: Float!
  lastPaymentDate: String
  maxUsers: Float
  packageName: String!
  paymentReference: String
  startDate: String!
  status: PaymentStatus!
  updatedAt: String!
}

"""Status of a payment"""
enum PaymentStatus {
  ACTIVE
  CANCELED
  EXPIRED
  PENDING
  TRIAL
}

type Permission {
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  id: Float!
  name: String!
  roles: [Role!]
  updatedAt: String!
  users: [User!]
}

type PermissionResponse {
  error: FieldError
  permission: Permission
  status: Boolean!
}

type Query {
  expenses(filter: ExpenseFilterInput): [Expense!]!
  getActivePayment(companyId: Float!): Payment
  getAllCategories: [Category!]!
  getAllItems: [Item!]!
  getAllMessages(endDate: DateTime!, startDate: DateTime!): [Message!]!
  getAllServices: [Item!]!
  getBatchStockForStore(itemId: Float, storeId: Float): [BatchStock!]!
  getCategories(type: String!): [Category!]!
  getCompanies: [Company!]!
  getCompany(id: Float!): Company
  getCompanyErrors(companyId: Int!, days: Int = 7, endDate: DateTime, severity: String, startDate: DateTime): [LogEntry!]!
  getDepartments: [Department!]!
  getDispatches: [Inventory!]!
  getEmployees: [User!]!
  getExpense(id: Float!): Expense
  getExpenses: [Expense!]!
  getFeatures: [Feature!]!
  getInternalItems: [Item!]!
  getInventoryTransfer(id: Float!): Inventory
  getInventoryTransfers(type: String): [Inventory!]!
  getItem(id: Float!): Item!
  getItemBatchImports(itemId: Float!): [Import!]!
  getItemBatchStocks(itemId: Float!): [BatchStock!]!
  getItemStoreStocks(itemId: Float!): [StoreItemStock!]!
  getItemTransfers(itemId: Float!, type: String): [Transfer!]!
  getItemUnits(itemId: Float!): [Unit!]!
  getItems: [Item!]!
  getLatestPayment: Payment
  getLogs(companyId: Int, endDate: DateTime, errorCode: String, level: String, startDate: DateTime, userId: Int): [LogEntry!]!
  getMerchandiseItems: [Item!]!
  getMessages(attended: Boolean): [Message!]!
  getOpenTabs: [Inventory!]!
  getPayment(id: Float!): Payment
  getPermissions: [Permission!]!
  getRole(name: String!): Role
  getRoles(sys: Boolean): [Role!]!
  getSales(date: DateTime): [Inventory!]!
  getSalesPOS: [Inventory!]!
  getStoreItems(storeId: Float!): [Item!]!
  getStores: [Store!]!
  getSyncConfig: SyncConfig!
  getTodaysErrors: [LogEntry!]!
  getTransfers: [Inventory!]!
  getType(id: Float!): Type
  getTypes: [Type!]!
  getUndetailedEmployees(companyId: Float!): [UndetailedUser!]!
  getUnit(id: Float!): Unit
  getUser(id: Float!): User
  getUsers(roles: [Float!]): [User!]!
  getWriteOffsByCompany: [Transfer!]!
  lastSyncStatus(entityName: String!): SyncHistory
  me: User
  payments: [Payment!]!
  syncHistory(companyId: Float!, entityName: String, limit: Float = 10): [SyncHistory!]!
}

input RegisterCompanyAddressedArgs {
  city: String!
  district: String!
  name: String!
  registrationNumber: String!
  street: String!
  tinNumber: String!
  type: String!
  ward: String!
}

input RegisterCompanyArgs {
  location: String
  name: String!
  registrationNumber: String!
  tinNumber: String!
  type: String!
}

input RegisterEmployeeArgs {
  companyRole: Float
  department: Float
  designation: String!
  email: String!
  firstname: String!
  lastname: String!
  licenseNumber: String!
  middlename: String!
  password: String!
  phone: String!
  store: Float
}

input RegisterUserArgs {
  companyId: Float!
  email: String!
  firstname: String!
  lastname: String!
  middlename: String!
  password: String!
  phone: String!
}

type Role {
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  employees: [Employee!]
  id: Float!
  name: String!
  permissions: [Permission!]
  sys: Boolean!
  updatedAt: String!
  users: [User!]
}

input RoleArgs {
  name: String!
  permissions: [Float!]
}

input SaleInput {
  batch: String
  hold: Boolean = false
  itemId: Float!
  quantity: Float!
  remarks: String
  unit: String!
}

input ServiceInput {
  description: String
  name: String!
  reference: String
  sellingPrice: Float!
}

type Store {
  address: String!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  id: Float!
  name: String!
  primary: Boolean!
  stockIn: [Inventory!]!
  stockOut: [Inventory!]!
  storeItemStocks: [StoreItemStock!]
  storeKeepers: [Employee!]
  updatedAt: String!
}

input StoreEditInput {
  address: String
  companyId: Float
  id: Float!
  name: String
  primary: Boolean
}

input StoreInput {
  address: String!
  name: String!
  primary: Boolean = false
}

type StoreItemStock {
  batchId: Float!
  batchStock: BatchStock
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  id: Float!
  item: Item!
  itemId: Float!
  stock: Float!
  store: Store!
  storeId: Float!
  updatedAt: String!
}

type SyncConfig {
  localCompanyId: String!
  remoteApiKey: String!
  remoteApiUrl: String!
  syncInterval: String!
  syncRetryAttempts: String!
  syncRetryDelay: String!
}

input SyncConfigInput {
  localCompanyId: String!
  remoteApiKey: String!
  remoteApiUrl: String!
  syncInterval: String!
  syncRetryAttempts: String!
  syncRetryDelay: String!
}

type SyncError {
  code: String
  details: String
  message: String!
  stack: String
}

type SyncHistory {
  company: Company!
  companyId: Float!
  createdAt: DateTime!
  direction: String!
  entityName: String!
  error: SyncError
  id: Float!
  lastSyncTimestamp: DateTime!
  recordsProcessed: Float!
  status: String!
  updatedAt: DateTime!
}

type Transfer {
  batch: String!
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  details: String
  dispatched: Boolean!
  granted: Boolean!
  id: Float!
  inventoryId: Float!
  inventoryTransfer: Inventory!
  item: Item!
  itemId: Float!
  price: Float!
  quantity: Float!
  received: Boolean!
  updatedAt: String!
}

input TransferInput {
  batch: String
  itemId: Float!
  quantity: Float!
  unit: String!
}

type TransferResponse {
  error: FieldError
  status: Boolean!
  transfer: Transfer
}

type Type {
  category: [Category!]
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  description: String!
  id: Float!
  name: String!
  updatedAt: String!
}

input TypeArgs {
  description: String!
  name: String!
}

input TypeEditArgs {
  categories: [Float!]!
  description: String!
  name: String!
}

type UndetailedUser {
  email: String!
  firstname: String!
  image: String
  lastname: String!
}

type Unit {
  companyId: Float!
  createdAt: String!
  deleted: Boolean!
  id: Float!
  item: Item!
  itemId: Float!
  name: String!
  price: Float!
  quantity: Float!
  updatedAt: String!
}

input UnitInput {
  itemId: Float!
  name: String!
  price: Float!
  quantity: Float!
}

input UpdatePaymentInput {
  amount: Float
  autoRenew: Boolean
  billingCycle: String
  cancellationReason: String
  endDate: DateTime
  features: String
  lastPaymentDate: DateTime
  maxUsers: Float
  packageName: String
  paymentReference: String
  startDate: DateTime
  status: String
}

type User {
  address: String!
  company: Company!
  companyId: Float!
  createdAt: String!
  dateOfBirth: String!
  deleted: Boolean!
  email: String
  employee: Employee
  firstname: String!
  gender: String!
  id: Float!
  image: String
  lastname: String!
  middlename: String!
  permissions: [Permission!]
  phone: String!
  role: Role!
  roleId: Float!
  status: [Category!]
  updatedAt: String!
}

type UserResponse {
  error: FieldError
  token: String
  user: User
}

input WriteOffInput {
  batch: String
  itemId: Float!
  locationId: Float!
  quantity: Float!
  reason: String
  unit: String!
}
