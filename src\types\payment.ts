import { Field, InputType, registerEnumType } from "type-graphql";

enum PaymentStatus {
  ACTIVE = "active",
  CANCELED = "canceled",
  EXPIRED = "expired",
  PENDING = "pending",
  TRIAL = "trial",
}

registerEnumType(PaymentStatus, {
  name: "PaymentStatus",
  description: "Status of a payment",
});

enum BillingCycle {
  MONTHLY = "monthly",
  QUARTERLY = "quarterly",
  ANNUALLY = "annually",
}

registerEnumType(BillingCycle, {
  name: "BillingCycle",
  description: "Billing cycle for payment",
});

@InputType()
export class CreatePaymentInput {
  @Field()
  packageName: string;

  @Field()
  status: PaymentStatus;

  @Field()
  billingCycle: BillingCycle;

  @Field()
  startDate: Date;

  @Field()
  endDate: Date;

  @Field()
  amount: number;

  @Field({ nullable: true })
  paymentReference?: string;

  @Field({ nullable: true })
  autoRenew?: boolean;

  @Field({ nullable: true })
  maxUsers?: number;

  @Field({ nullable: true })
  features?: string;
}

@InputType()
export class UpdatePaymentInput {
  @Field({ nullable: true })
  packageName?: string;

  @Field({ nullable: true })
  status?: PaymentStatus;

  @Field({ nullable: true })
  billingCycle?: BillingCycle;

  @Field({ nullable: true })
  startDate?: Date;

  @Field({ nullable: true })
  endDate?: Date;

  @Field({ nullable: true })
  amount?: number;

  @Field({ nullable: true })
  paymentReference?: string;

  @Field({ nullable: true })
  autoRenew?: boolean;

  @Field({ nullable: true })
  maxUsers?: number;

  @Field({ nullable: true })
  features?: string;

  @Field({ nullable: true })
  lastPaymentDate?: Date;

  @Field({ nullable: true })
  cancellationReason?: string;
}
