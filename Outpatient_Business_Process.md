Outpatient Business Process

PHASE I:
After patient being registered to the system, then he/she will be assigned to the clinic where we determine health insurance provider and process the complience policy for a particular insurance and create a consultation bill to be billed by a cashier or confirmed by the service provider personnel in the hospital. The patient visit will be created and vital signs must be recorded by a nurse  as a next step before the patient go to see the doctor.


Both of these inherit CommonModel
NB. CRUD functionality is to be implemented before designing the forms layout.

Models Associated 


- Directory model
1. Id
2. HOD
3. Name
4. Status
5. Description

- Insurance model
1. Id
2. Code
3. Name
4. Status
5. Description
6. Type

- InsuranceScheme model
1. Id
2. Code
3. Name
4. Status
5. Description
6. Type
7. ProductCode
8. InsuranceID

- PriceList model
1. Id
2. Code
3. Name
4. Status
5. Description
6. Type
7. InsuranceID
8. InsuranceSchemeID
9. PriceCoverage
10. PriceMarginType
11. Amount




- Visit model
1. Id
2. PatientID
3. VisitType
4. InsuranceID
5. InsuranceCardNumber
6. AuthorizationNumber
7. InsuranceSchemeID
8. ProductCode
9. FolioID
10. FolioNumber
11. Consultation
12. CheckInType
13. CheckOutType
14. CheckInTime
15. CheckOutTime
16. QueueNumber
17. Status
18. CheckInStatus
19. CheckOutStatus
20. PreliminaryDx
21. ConfirmedDx

- Department model
1. Id
2. HOD
3. Name
4. Status
5. Description
6. Type
7. DirectoryID

- SubDepartment model
1. Id
2. HOD
3. Name
4. Status
5. Description
6. Type
7. DepartmentID

- Clinic model
1. Id
2. DepartmentID
3. Size
4. Leader
4. ClinicType
5. Status
6. OpenTime
7. CloseTime
8. WorkingDays
9. Name
10. Description



- Vitals model
1. Id
2. VisitID
3. PatientID
4. Diastolic Pressure
5. Systolic Pressure
6. Pulse
7. Tempurature
9. Weight
10. Height
11. Oxygen
12. Glucose
13. Mood



- Staff model
1. Id
2. EmployeeNumber
3. FirstName
3. MiddleName
4. LastName
5. Email
6. Profession
7. Designation
8. Role
9. Department
10. DateOfJoining
11. DateOfLeaving
12. PhoneNumber
13. Status


- Audit model
1. Id
2. Subject 
3. Action
4. Resource
5. Time


- Folio model
1. Id
2. Number
3. PatientId
4. Insurance
5. InsuranceCardNumber
6. BillDate
7. CoverageStartDate
8. CoverageEndDate
9. TotalAmountBilled
10. AmountCoveredByInsurance
11. AmountPaidByPatient
12. OutstandingAmount
13. FolioItems
14. PaymentStatus
15. PaymentMethod
16. ContactInformation
17. LateSubmissionReason
18. PrelimaryDx
19. ConfirmedDx
20. Status
21. PractionerSignature
22. PatientSignature
23. Clinical Notes
24. PractionerMCTNumber
25. Tests

