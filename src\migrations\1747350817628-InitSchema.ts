import { MigrationInterface, QueryRunner } from "typeorm";
import { dragon } from "../utils/bcrypt-wrapper";

export class InitSchema1747350817628 implements MigrationInterface {
  name = "InitSchema1747350817628";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "audit_base_entity" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, CONSTRAINT "UQ_e870b9d8c26367a1d8f6ed41833" UNIQUE ("id", "companyId"), CONSTRAINT "PK_c7e45c997065acdc5bce7ed09ab" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "feature" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "name" character varying NOT NULL, CONSTRAINT "UQ_fba3269d426ebef749dc31c9d82" UNIQUE ("id", "companyId"), CONSTRAINT "UQ_4832be692a2dc63d67e8e93c758" UNIQUE ("name"), CONSTRAINT "PK_03930932f909ca4be8e33d16a2d" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "permission" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "name" character varying NOT NULL, CONSTRAINT "UQ_cdd8bcfff8ae526bbffa6876715" UNIQUE ("id", "companyId"), CONSTRAINT "UQ_765058fbc14686614799fd590ea" UNIQUE ("companyId", "name"), CONSTRAINT "PK_3b8b97af9d9d8807e41e6f48362" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "role" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "sys" boolean NOT NULL DEFAULT false, "name" text NOT NULL, CONSTRAINT "UQ_7be62fc8217922a02cbb6250b56" UNIQUE ("id", "companyId"), CONSTRAINT "UQ_76fa8611fc76f42d67b0268e4e2" UNIQUE ("name", "companyId"), CONSTRAINT "PK_b36bcfe02fc8de3c57a8b2391c2" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "type" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "name" text NOT NULL, "description" text NOT NULL, CONSTRAINT "UQ_78e6430d4e82643df98e9764e1f" UNIQUE ("id", "companyId"), CONSTRAINT "UQ_c9c584ce9e0c675d3611fc221da" UNIQUE ("name", "companyId"), CONSTRAINT "PK_40410d6bf0bedb43f9cadae6fef" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "category" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "name" text NOT NULL, "typeId" integer NOT NULL, "parentCategoryId" integer, "type_category" text NOT NULL, CONSTRAINT "UQ_5546d3381537abdbaae386bdf8e" UNIQUE ("id", "companyId"), CONSTRAINT "PK_9c4e4a89e3674fc9f382d733f03" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_1759b664f9fdf5788e2b0af044" ON "category" ("type_category") `
    );
    await queryRunner.query(
      `CREATE TYPE "public"."user_gender_enum" AS ENUM('male', 'female', 'other')`
    );
    await queryRunner.query(
      `CREATE TABLE "user" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "firstname" text NOT NULL, "middlename" text NOT NULL, "lastname" text NOT NULL, "email" text NOT NULL, "phone" text NOT NULL, "gender" "public"."user_gender_enum" NOT NULL DEFAULT 'other', "dateOfBirth" text, "image" text, "address" text, "roleId" integer NOT NULL, "password" text NOT NULL DEFAULT 'halisia', CONSTRAINT "UQ_cd26319c974f8a37b403883aaf9" UNIQUE ("id", "companyId"), CONSTRAINT "UQ_e12875dfb3b1d92d7d7c5377e22" UNIQUE ("email"), CONSTRAINT "UQ_8e1f623798118e629b46a9e6299" UNIQUE ("phone"), CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "department" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "name" text NOT NULL, "description" text NOT NULL, "status" text, "parentId" integer, "type" text, CONSTRAINT "UQ_9f93787f70fcf476dd33a4789d8" UNIQUE ("id", "companyId"), CONSTRAINT "PK_9a2213262c1593bffb581e382f5" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "sync_history" ("id" SERIAL NOT NULL, "entityName" character varying NOT NULL, "direction" character varying NOT NULL, "status" character varying NOT NULL, "error" json, "recordsProcessed" integer NOT NULL, "lastSyncTimestamp" TIMESTAMP NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_e929aeba0c2244394dab3a7514c" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TYPE "public"."payment_status_enum" AS ENUM('active', 'canceled', 'expired', 'pending', 'trial')`
    );
    await queryRunner.query(
      `CREATE TYPE "public"."payment_billingcycle_enum" AS ENUM('monthly', 'quarterly', 'annually')`
    );
    await queryRunner.query(
      `CREATE TABLE "payment" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "packageName" text NOT NULL DEFAULT 'basic', "status" "public"."payment_status_enum" NOT NULL DEFAULT 'pending', "billingCycle" "public"."payment_billingcycle_enum" NOT NULL DEFAULT 'monthly', "startDate" TIMESTAMP NOT NULL, "endDate" TIMESTAMP NOT NULL, "amount" bigint NOT NULL, "paymentReference" text, "autoRenew" boolean NOT NULL DEFAULT false, "maxUsers" integer, "features" text, "lastPaymentDate" TIMESTAMP, "cancellationReason" text, CONSTRAINT "UQ_b48693ac6ed1d46bce4a018775d" UNIQUE ("id", "companyId"), CONSTRAINT "PK_fcaec7df5adf9cac408c686b2ab" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "company" ("id" SERIAL NOT NULL, "companyId" integer, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "isParent" boolean NOT NULL DEFAULT false, "isBranch" boolean NOT NULL DEFAULT false, "parentId" integer, "branches" integer, "name" text NOT NULL, "syncUrl" text, "tinNumber" text NOT NULL, "registrationNumber" text NOT NULL, "type" text NOT NULL, "phone" text, "email" text, "poBox" text, "logo" text, "website" text, "location" text NOT NULL, CONSTRAINT "UQ_a76c5cd486f7779bd9c319afd27" UNIQUE ("name"), CONSTRAINT "PK_056f7854a7afdba7cbd6d45fc20" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "import" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "importDate" TIMESTAMP NOT NULL, "expireDate" TIMESTAMP, "supplier" text NOT NULL, "quantity" double precision NOT NULL DEFAULT '0', "unit" text NOT NULL, "importPrice" bigint, "sellingPrice" bigint NOT NULL, "receipt" text, "itemId" integer NOT NULL, "batch" text, CONSTRAINT "UQ_14542f9116496d5fb2af02073a5" UNIQUE ("id", "companyId"), CONSTRAINT "PK_4ed733f5bcc70cec27187bd90eb" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "transfer" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "inventoryId" integer NOT NULL, "itemId" integer NOT NULL, "price" integer NOT NULL DEFAULT '0', "granted" boolean NOT NULL DEFAULT true, "received" boolean NOT NULL DEFAULT true, "dispatched" boolean NOT NULL DEFAULT true, "quantity" double precision NOT NULL, "details" text, "batch" text, CONSTRAINT "UQ_71adc756057db02e49e39d80fbf" UNIQUE ("id", "companyId"), CONSTRAINT "PK_fd9ddbdd49a17afcbe014401295" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "batch_stock" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "itemId" integer NOT NULL, "batch" text NOT NULL, "expireDate" text NOT NULL, "stock" double precision NOT NULL DEFAULT '0', CONSTRAINT "UQ_722abc114d10dafc9bf1bef134f" UNIQUE ("id", "companyId"), CONSTRAINT "PK_da85ae713a02d5a5a7cd1c7300b" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "unit" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "name" text NOT NULL, "quantity" double precision NOT NULL, "price" bigint NOT NULL DEFAULT '0', "itemId" integer NOT NULL, CONSTRAINT "UQ_602f66180b77b805fb084a4b212" UNIQUE ("id", "companyId"), CONSTRAINT "UQ_d09de493a2e0168143e53282f61" UNIQUE ("companyId", "name", "itemId"), CONSTRAINT "PK_4252c4be609041e559f0c80f58a" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TYPE "public"."item_type_enum" AS ENUM('consumable', 'capital goods', 'service')`
    );
    await queryRunner.query(
      `CREATE TABLE "item" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "name" text NOT NULL, "description" text NOT NULL, "barcode" text, "type" "public"."item_type_enum" NOT NULL DEFAULT 'consumable', "reference" text NOT NULL, "reorder" integer NOT NULL DEFAULT '5', "internal" boolean NOT NULL DEFAULT false, "image" text NOT NULL DEFAULT 'https://eqzgvivfuzmyfxbupxht.supabase.co/storage/v1/object/public/heal/public/watermelon-removebg-preview.png', "unit" character varying NOT NULL, "stock" double precision NOT NULL DEFAULT '0', "sellingPrice" bigint NOT NULL DEFAULT '0', CONSTRAINT "UQ_ef3102a893e36c62dbbf7653f40" UNIQUE ("id", "companyId"), CONSTRAINT "UQ_9600c4949ee672a90779e8ab18f" UNIQUE ("companyId", "name"), CONSTRAINT "PK_d3c0c71f23e7adcf952a1d13423" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "store_item_stock" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "itemId" integer NOT NULL, "storeId" integer NOT NULL, "stock" double precision NOT NULL DEFAULT '0', "batchId" integer, CONSTRAINT "UQ_92ab6c87c3154ad4082771c9de5" UNIQUE ("id", "companyId"), CONSTRAINT "PK_566eac7db8d3a7524b5455ce78b" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "store" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "name" text NOT NULL, "primary" boolean NOT NULL DEFAULT false, "address" text NOT NULL, CONSTRAINT "UQ_1def9be0a6e36a6d742aad9a04c" UNIQUE ("id", "companyId"), CONSTRAINT "UQ_4328d18fdc5f57d1dfa468b1001" UNIQUE ("companyId", "name"), CONSTRAINT "PK_f3172007d4de5ae8e7692759d79" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TYPE "public"."bill_paymenttype_enum" AS ENUM('cash', 'insurance')`
    );
    await queryRunner.query(
      `CREATE TABLE "bill" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "cleared" boolean NOT NULL DEFAULT false, "inventoryId" integer NOT NULL, "amount" integer NOT NULL DEFAULT '0', "paymentType" "public"."bill_paymenttype_enum" NOT NULL DEFAULT 'cash', CONSTRAINT "UQ_a29c4b01737bac66f515e523490" UNIQUE ("id", "companyId"), CONSTRAINT "REL_b1c02e5c50c9dc5868fece9b39" UNIQUE ("inventoryId", "companyId"), CONSTRAINT "PK_683b47912b8b30fe71d1fa22199" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TYPE "public"."inventory_type_enum" AS ENUM('purchase', 'dispatch', 'bill', 'sale', 'transfer', 'writeOff', 'returnToVendor')`
    );
    await queryRunner.query(
      `CREATE TABLE "inventory" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "details" text, "customerTag" text, "type" "public"."inventory_type_enum" NOT NULL DEFAULT 'transfer', "granted" boolean NOT NULL DEFAULT false, "received" boolean NOT NULL DEFAULT false, "dispatched" boolean NOT NULL DEFAULT false, "transferDate" TIMESTAMP NOT NULL, "returnDate" TIMESTAMP, "startDate" TIMESTAMP, "sourceStoreId" integer, "destinationStoreId" integer, "keeperId" integer, "consumerId" integer, "approverId" integer, CONSTRAINT "UQ_5c113294f75ad9914c1b68d0c0c" UNIQUE ("id", "companyId"), CONSTRAINT "PK_82aa5da437c5bbfb80703b08309" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TYPE "public"."expense_assettype_enum" AS ENUM('office', 'employee', 'other')`
    );
    await queryRunner.query(
      `CREATE TYPE "public"."expense_type_enum" AS ENUM('debit', 'credit')`
    );
    await queryRunner.query(
      `CREATE TYPE "public"."expense_status_enum" AS ENUM('requested', 'approved')`
    );
    await queryRunner.query(
      `CREATE TABLE "expense" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "expenseDate" TIMESTAMP NOT NULL, "authorizerId" integer, "requesterId" integer NOT NULL, "assetId" integer, "assetType" "public"."expense_assettype_enum" NOT NULL DEFAULT 'other', "type" "public"."expense_type_enum" NOT NULL DEFAULT 'credit', "status" "public"."expense_status_enum" NOT NULL DEFAULT 'requested', "title" text NOT NULL, "details" text NOT NULL, "amount" bigint NOT NULL, CONSTRAINT "UQ_16a2a23f890a7a7ef7ac452911d" UNIQUE ("id", "companyId"), CONSTRAINT "PK_edd925b450e13ea36197c9590fc" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "employee" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "status" text NOT NULL DEFAULT 'NEW', "roleId" integer, "designation" text NOT NULL, "licenceNumber" text NOT NULL, "image" text, "userId" integer NOT NULL, "storeId" integer, "departmentId" integer, "headingDepartmentId" integer, CONSTRAINT "UQ_ea489af994b698a393cceec05f3" UNIQUE ("id", "companyId"), CONSTRAINT "REL_f6dd582d2028f14b8dbe3b3874" UNIQUE ("userId", "companyId"), CONSTRAINT "REL_1135a40b6618dd802c5c1ec09f" UNIQUE ("headingDepartmentId", "companyId"), CONSTRAINT "PK_3c2bc72f03fd5abbbc5ac169498" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TYPE "public"."approval_feature_enum" AS ENUM('INVENTORY')`
    );
    await queryRunner.query(
      `CREATE TYPE "public"."approval_type_enum" AS ENUM('TRANSFER')`
    );
    await queryRunner.query(
      `CREATE TABLE "approval" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "feature" "public"."approval_feature_enum" NOT NULL, "type" "public"."approval_type_enum" NOT NULL, "status" boolean NOT NULL DEFAULT false, "requesterId" integer, "approverId" integer, "approvalDate" TIMESTAMP, "requestId" integer NOT NULL, CONSTRAINT "UQ_b3dea2fac2b00b8060e05fa5ec8" UNIQUE ("id", "companyId"), CONSTRAINT "PK_97bfd1cd9dff3c1302229da6b5c" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "error_log" ("id" SERIAL NOT NULL, "companyId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deleted" boolean NOT NULL DEFAULT false, "errorMessage" text NOT NULL, "errorCode" text NOT NULL, "errorStackTrace" text NOT NULL, "severity" text NOT NULL, "userId" integer, "action" text NOT NULL, "resolved" boolean NOT NULL DEFAULT false, CONSTRAINT "UQ_a9fbd1e75da2202d349c76cf657" UNIQUE ("id", "companyId"), CONSTRAINT "PK_0284e7aa7afe77ea1ce1621c252" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "message" ("id" SERIAL NOT NULL, "attended" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "senderName" text NOT NULL, "senderEmail" text NOT NULL, "senderPhone" text, "subject" text NOT NULL, "message" text NOT NULL, CONSTRAINT "PK_ba01f0a3e0123651915008bc578" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "company_features" ("featureId" integer NOT NULL, "companyId_1" integer NOT NULL, "companyId_2" integer NOT NULL, CONSTRAINT "PK_a7b6a47e421e850abb40a68c6f1" PRIMARY KEY ("featureId", "companyId_1", "companyId_2"))`
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_cfcf09d300efc2b636f12399c9" ON "company_features" ("featureId", "companyId_1") `
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d1555adb93eb57ec97f002a84d" ON "company_features" ("companyId_2") `
    );
    await queryRunner.query(
      `CREATE TABLE "role_permission" ("permissionId" integer NOT NULL, "companyId_1" integer NOT NULL, "roleId" integer NOT NULL, "companyId_2" integer NOT NULL, CONSTRAINT "PK_676edf88e7999ed878ae0c0f4df" PRIMARY KEY ("permissionId", "companyId_1", "roleId", "companyId_2"))`
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_35de61c5d69dc685b6284842c4" ON "role_permission" ("permissionId", "companyId_1") `
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2fbbf6a2ab38cb5ca872f6d0b6" ON "role_permission" ("roleId", "companyId_2") `
    );
    await queryRunner.query(
      `CREATE TABLE "user_permission" ("permissionId" integer NOT NULL, "companyId_1" integer NOT NULL, "userId" integer NOT NULL, "companyId_2" integer NOT NULL, CONSTRAINT "PK_dc98e05c1acd17bc36d3a0b2d74" PRIMARY KEY ("permissionId", "companyId_1", "userId", "companyId_2"))`
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6b283b881ee62e84f460626188" ON "user_permission" ("permissionId", "companyId_1") `
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_bc17cead6f5c7ab11e6de99cb7" ON "user_permission" ("userId", "companyId_2") `
    );
    await queryRunner.query(
      `CREATE TABLE "user_category" ("categoryId" integer NOT NULL, "companyId_1" integer NOT NULL, "userId" integer NOT NULL, "companyId_2" integer NOT NULL, CONSTRAINT "PK_bef7a7411328a40ce7a2e96887e" PRIMARY KEY ("categoryId", "companyId_1", "userId", "companyId_2"))`
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e31a35fa2193cb162184eb4f66" ON "user_category" ("categoryId", "companyId_1") `
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_29ee19f78a69d288f346c8336c" ON "user_category" ("userId", "companyId_2") `
    );
    await queryRunner.query(
      `CREATE TABLE "inventory_items" ("inventoryId" integer NOT NULL, "companyId_1" integer NOT NULL, "itemId" integer NOT NULL, "companyId_2" integer NOT NULL, CONSTRAINT "PK_1d5d03ba53ab094ffd5eec52f11" PRIMARY KEY ("inventoryId", "companyId_1", "itemId", "companyId_2"))`
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b518131f4a07e9821495135392" ON "inventory_items" ("inventoryId", "companyId_1") `
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d2daa087cfd6264b2e34aa88b4" ON "inventory_items" ("itemId", "companyId_2") `
    );
    await queryRunner.query(
      `ALTER TABLE "category" ADD CONSTRAINT "FK_881d21797a1c0fdca3f215f4fa0" FOREIGN KEY ("typeId", "companyId") REFERENCES "type"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "FK_86586021a26d1180b0968f98502" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "FK_225fa9503c5519ade2dfe126d4f" FOREIGN KEY ("roleId", "companyId") REFERENCES "role"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "department" ADD CONSTRAINT "FK_1c9f0159b4ae69008bd356bb1ce" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "sync_history" ADD CONSTRAINT "FK_3bbc1abae5db9847d22602432b3" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "payment" ADD CONSTRAINT "FK_0fe9e707d9586ed82ea31cabd17" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "import" ADD CONSTRAINT "FK_cb19e2ce1a378033a38fcef072b" FOREIGN KEY ("itemId", "companyId") REFERENCES "item"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "transfer" ADD CONSTRAINT "FK_2a677bff117e235b4bc6c9184bd" FOREIGN KEY ("inventoryId", "companyId") REFERENCES "inventory"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "transfer" ADD CONSTRAINT "FK_022ac7c95b305535d3dc2644dfd" FOREIGN KEY ("itemId", "companyId") REFERENCES "item"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "batch_stock" ADD CONSTRAINT "FK_2bb64d307f220f5fc777547e6b5" FOREIGN KEY ("itemId", "companyId") REFERENCES "item"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "unit" ADD CONSTRAINT "FK_dee416bf625fdf49fea03023901" FOREIGN KEY ("itemId", "companyId") REFERENCES "item"("id","companyId") ON DELETE CASCADE ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "store_item_stock" ADD CONSTRAINT "FK_af738921b559ada9b2e1d385ce7" FOREIGN KEY ("itemId", "companyId") REFERENCES "item"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "store_item_stock" ADD CONSTRAINT "FK_8ac2609c34ad09825eef6f9215b" FOREIGN KEY ("storeId", "companyId") REFERENCES "store"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "store_item_stock" ADD CONSTRAINT "FK_d649fabcf962d543c41c138eae3" FOREIGN KEY ("batchId", "companyId") REFERENCES "batch_stock"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "bill" ADD CONSTRAINT "FK_b1c02e5c50c9dc5868fece9b392" FOREIGN KEY ("inventoryId", "companyId") REFERENCES "inventory"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "inventory" ADD CONSTRAINT "FK_becfdbd89976467a4e3acddd06f" FOREIGN KEY ("sourceStoreId", "companyId") REFERENCES "store"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "inventory" ADD CONSTRAINT "FK_7c0231da28bcb122921427e4a08" FOREIGN KEY ("destinationStoreId", "companyId") REFERENCES "store"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "inventory" ADD CONSTRAINT "FK_1aae029e361acfaf97b8c1963be" FOREIGN KEY ("keeperId", "companyId") REFERENCES "employee"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "inventory" ADD CONSTRAINT "FK_2f61a59ceee426fa8e807bab01f" FOREIGN KEY ("consumerId", "companyId") REFERENCES "employee"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "inventory" ADD CONSTRAINT "FK_b50222ad2531950388371e47068" FOREIGN KEY ("approverId", "companyId") REFERENCES "employee"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "expense" ADD CONSTRAINT "FK_9cb752aeab8c3e9a680f27d70ad" FOREIGN KEY ("authorizerId", "companyId") REFERENCES "employee"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "expense" ADD CONSTRAINT "FK_d646ca37d54a5390959f53684dd" FOREIGN KEY ("requesterId", "companyId") REFERENCES "employee"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "employee" ADD CONSTRAINT "FK_49e13a52783940b07ba8dcbb288" FOREIGN KEY ("roleId", "companyId") REFERENCES "role"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "employee" ADD CONSTRAINT "FK_f6dd582d2028f14b8dbe3b38748" FOREIGN KEY ("userId", "companyId") REFERENCES "user"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "employee" ADD CONSTRAINT "FK_26c3d513e0832e5abbbdd3d2a88" FOREIGN KEY ("companyId") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "employee" ADD CONSTRAINT "FK_14bc735f6dce4c16fdd51aaedae" FOREIGN KEY ("storeId", "companyId") REFERENCES "store"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "employee" ADD CONSTRAINT "FK_aec1faa3e6e3b63e49e9cf75659" FOREIGN KEY ("departmentId", "companyId") REFERENCES "department"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "employee" ADD CONSTRAINT "FK_1135a40b6618dd802c5c1ec09fc" FOREIGN KEY ("headingDepartmentId", "companyId") REFERENCES "department"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "approval" ADD CONSTRAINT "FK_f95403ec0a485b961b7875ae2c1" FOREIGN KEY ("requesterId", "companyId") REFERENCES "employee"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "approval" ADD CONSTRAINT "FK_2def51ec19f67b28440eca2ca13" FOREIGN KEY ("approverId", "companyId") REFERENCES "employee"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "company_features" ADD CONSTRAINT "FK_cfcf09d300efc2b636f12399c91" FOREIGN KEY ("featureId", "companyId_1") REFERENCES "feature"("id","companyId") ON DELETE CASCADE ON UPDATE CASCADE`
    );
    await queryRunner.query(
      `ALTER TABLE "company_features" ADD CONSTRAINT "FK_d1555adb93eb57ec97f002a84da" FOREIGN KEY ("companyId_2") REFERENCES "company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "role_permission" ADD CONSTRAINT "FK_35de61c5d69dc685b6284842c41" FOREIGN KEY ("permissionId", "companyId_1") REFERENCES "permission"("id","companyId") ON DELETE CASCADE ON UPDATE CASCADE`
    );
    await queryRunner.query(
      `ALTER TABLE "role_permission" ADD CONSTRAINT "FK_2fbbf6a2ab38cb5ca872f6d0b60" FOREIGN KEY ("roleId", "companyId_2") REFERENCES "role"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "user_permission" ADD CONSTRAINT "FK_6b283b881ee62e84f4606261881" FOREIGN KEY ("permissionId", "companyId_1") REFERENCES "permission"("id","companyId") ON DELETE CASCADE ON UPDATE CASCADE`
    );
    await queryRunner.query(
      `ALTER TABLE "user_permission" ADD CONSTRAINT "FK_bc17cead6f5c7ab11e6de99cb73" FOREIGN KEY ("userId", "companyId_2") REFERENCES "user"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "user_category" ADD CONSTRAINT "FK_e31a35fa2193cb162184eb4f66b" FOREIGN KEY ("categoryId", "companyId_1") REFERENCES "category"("id","companyId") ON DELETE CASCADE ON UPDATE CASCADE`
    );
    await queryRunner.query(
      `ALTER TABLE "user_category" ADD CONSTRAINT "FK_29ee19f78a69d288f346c8336cd" FOREIGN KEY ("userId", "companyId_2") REFERENCES "user"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "inventory_items" ADD CONSTRAINT "FK_b518131f4a07e98214951353926" FOREIGN KEY ("inventoryId", "companyId_1") REFERENCES "inventory"("id","companyId") ON DELETE CASCADE ON UPDATE CASCADE`
    );
    await queryRunner.query(
      `ALTER TABLE "inventory_items" ADD CONSTRAINT "FK_d2daa087cfd6264b2e34aa88b40" FOREIGN KEY ("itemId", "companyId_2") REFERENCES "item"("id","companyId") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );

    // Create default company and admin user
    try {
      // Check if company with id 0 exists
      const companyExists = await queryRunner.query(
        `SELECT * FROM "company" WHERE "id" = 0`
      );

      // If company doesn't exist, create it
      if (companyExists.length === 0) {
        await queryRunner.query(`
                    INSERT INTO "company" ("id", "companyId", "createdAt", "updatedAt", "deleted", "isParent", "isBranch", "name", "tinNumber", "registrationNumber", "type", "location")
                    VALUES (0, 0, NOW(), NOW(), false, true, false, 'Talisia POS Management', 'DEFAULT-TIN', 'DEFAULT-REG', 'POS', 'Tanzania')
                `);

        console.log("Default company created");
      }

      // Check if admin role exists for company 0
      const roleExists = await queryRunner.query(
        `SELECT * FROM "role" WHERE "companyId" = 0 AND "name" = 'admin'`
      );

      let adminRoleId: number;

      // If admin role doesn't exist, create it
      if (roleExists.length === 0) {
        const roleResult = await queryRunner.query(`
                    INSERT INTO "role" ("name", "companyId", "createdAt", "updatedAt", "deleted", "sys")
                    VALUES ('admin', 0, NOW(), NOW(), false, true)
                    RETURNING "id"
                `);

        adminRoleId = roleResult[0].id;
        console.log("Admin role created with ID:", adminRoleId);
      } else {
        adminRoleId = roleExists[0].id;
      }

      // Check if admin user exists
      const userExists = await queryRunner.query(
        `SELECT * FROM "user" WHERE "email" = '<EMAIL>'`
      );

      // If admin user doesn't exist, create it
      if (userExists.length === 0) {
        try {
          // Hash the password
          const hashedPassword = await dragon.hash("123123");

          await queryRunner.query(
            `
                        INSERT INTO "user" ("firstname", "middlename", "lastname", "email", "phone", "password", "companyId", "roleId", "createdAt", "updatedAt", "deleted")
                        VALUES ('John', '', 'Doe', '<EMAIL>', '0744648170', $1, 0, $2, NOW(), NOW(), false)
                    `,
            [hashedPassword, adminRoleId]
          );

          console.log("Admin user created");
        } catch (error) {
          console.error("Failed to create admin user:", error.message);
          // Continue execution - don't throw error to allow partial migration
        }
      }
    } catch (error) {
      console.error("Migration failed:", error.message);
      // Re-throw the error to indicate migration failure
      throw error;
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "inventory_items" DROP CONSTRAINT "FK_d2daa087cfd6264b2e34aa88b40"`
    );
    await queryRunner.query(
      `ALTER TABLE "inventory_items" DROP CONSTRAINT "FK_b518131f4a07e98214951353926"`
    );
    await queryRunner.query(
      `ALTER TABLE "user_category" DROP CONSTRAINT "FK_29ee19f78a69d288f346c8336cd"`
    );
    await queryRunner.query(
      `ALTER TABLE "user_category" DROP CONSTRAINT "FK_e31a35fa2193cb162184eb4f66b"`
    );
    await queryRunner.query(
      `ALTER TABLE "user_permission" DROP CONSTRAINT "FK_bc17cead6f5c7ab11e6de99cb73"`
    );
    await queryRunner.query(
      `ALTER TABLE "user_permission" DROP CONSTRAINT "FK_6b283b881ee62e84f4606261881"`
    );
    await queryRunner.query(
      `ALTER TABLE "role_permission" DROP CONSTRAINT "FK_2fbbf6a2ab38cb5ca872f6d0b60"`
    );
    await queryRunner.query(
      `ALTER TABLE "role_permission" DROP CONSTRAINT "FK_35de61c5d69dc685b6284842c41"`
    );
    await queryRunner.query(
      `ALTER TABLE "company_features" DROP CONSTRAINT "FK_d1555adb93eb57ec97f002a84da"`
    );
    await queryRunner.query(
      `ALTER TABLE "company_features" DROP CONSTRAINT "FK_cfcf09d300efc2b636f12399c91"`
    );
    await queryRunner.query(
      `ALTER TABLE "approval" DROP CONSTRAINT "FK_2def51ec19f67b28440eca2ca13"`
    );
    await queryRunner.query(
      `ALTER TABLE "approval" DROP CONSTRAINT "FK_f95403ec0a485b961b7875ae2c1"`
    );
    await queryRunner.query(
      `ALTER TABLE "employee" DROP CONSTRAINT "FK_1135a40b6618dd802c5c1ec09fc"`
    );
    await queryRunner.query(
      `ALTER TABLE "employee" DROP CONSTRAINT "FK_aec1faa3e6e3b63e49e9cf75659"`
    );
    await queryRunner.query(
      `ALTER TABLE "employee" DROP CONSTRAINT "FK_14bc735f6dce4c16fdd51aaedae"`
    );
    await queryRunner.query(
      `ALTER TABLE "employee" DROP CONSTRAINT "FK_26c3d513e0832e5abbbdd3d2a88"`
    );
    await queryRunner.query(
      `ALTER TABLE "employee" DROP CONSTRAINT "FK_f6dd582d2028f14b8dbe3b38748"`
    );
    await queryRunner.query(
      `ALTER TABLE "employee" DROP CONSTRAINT "FK_49e13a52783940b07ba8dcbb288"`
    );
    await queryRunner.query(
      `ALTER TABLE "expense" DROP CONSTRAINT "FK_d646ca37d54a5390959f53684dd"`
    );
    await queryRunner.query(
      `ALTER TABLE "expense" DROP CONSTRAINT "FK_9cb752aeab8c3e9a680f27d70ad"`
    );
    await queryRunner.query(
      `ALTER TABLE "inventory" DROP CONSTRAINT "FK_b50222ad2531950388371e47068"`
    );
    await queryRunner.query(
      `ALTER TABLE "inventory" DROP CONSTRAINT "FK_2f61a59ceee426fa8e807bab01f"`
    );
    await queryRunner.query(
      `ALTER TABLE "inventory" DROP CONSTRAINT "FK_1aae029e361acfaf97b8c1963be"`
    );
    await queryRunner.query(
      `ALTER TABLE "inventory" DROP CONSTRAINT "FK_7c0231da28bcb122921427e4a08"`
    );
    await queryRunner.query(
      `ALTER TABLE "inventory" DROP CONSTRAINT "FK_becfdbd89976467a4e3acddd06f"`
    );
    await queryRunner.query(
      `ALTER TABLE "bill" DROP CONSTRAINT "FK_b1c02e5c50c9dc5868fece9b392"`
    );
    await queryRunner.query(
      `ALTER TABLE "store_item_stock" DROP CONSTRAINT "FK_d649fabcf962d543c41c138eae3"`
    );
    await queryRunner.query(
      `ALTER TABLE "store_item_stock" DROP CONSTRAINT "FK_8ac2609c34ad09825eef6f9215b"`
    );
    await queryRunner.query(
      `ALTER TABLE "store_item_stock" DROP CONSTRAINT "FK_af738921b559ada9b2e1d385ce7"`
    );
    await queryRunner.query(
      `ALTER TABLE "unit" DROP CONSTRAINT "FK_dee416bf625fdf49fea03023901"`
    );
    await queryRunner.query(
      `ALTER TABLE "batch_stock" DROP CONSTRAINT "FK_2bb64d307f220f5fc777547e6b5"`
    );
    await queryRunner.query(
      `ALTER TABLE "transfer" DROP CONSTRAINT "FK_022ac7c95b305535d3dc2644dfd"`
    );
    await queryRunner.query(
      `ALTER TABLE "transfer" DROP CONSTRAINT "FK_2a677bff117e235b4bc6c9184bd"`
    );
    await queryRunner.query(
      `ALTER TABLE "import" DROP CONSTRAINT "FK_cb19e2ce1a378033a38fcef072b"`
    );
    await queryRunner.query(
      `ALTER TABLE "payment" DROP CONSTRAINT "FK_0fe9e707d9586ed82ea31cabd17"`
    );
    await queryRunner.query(
      `ALTER TABLE "sync_history" DROP CONSTRAINT "FK_3bbc1abae5db9847d22602432b3"`
    );
    await queryRunner.query(
      `ALTER TABLE "department" DROP CONSTRAINT "FK_1c9f0159b4ae69008bd356bb1ce"`
    );
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "FK_225fa9503c5519ade2dfe126d4f"`
    );
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "FK_86586021a26d1180b0968f98502"`
    );
    await queryRunner.query(
      `ALTER TABLE "category" DROP CONSTRAINT "FK_881d21797a1c0fdca3f215f4fa0"`
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_d2daa087cfd6264b2e34aa88b4"`
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b518131f4a07e9821495135392"`
    );
    await queryRunner.query(`DROP TABLE "inventory_items"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_29ee19f78a69d288f346c8336c"`
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e31a35fa2193cb162184eb4f66"`
    );
    await queryRunner.query(`DROP TABLE "user_category"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_bc17cead6f5c7ab11e6de99cb7"`
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_6b283b881ee62e84f460626188"`
    );
    await queryRunner.query(`DROP TABLE "user_permission"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2fbbf6a2ab38cb5ca872f6d0b6"`
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_35de61c5d69dc685b6284842c4"`
    );
    await queryRunner.query(`DROP TABLE "role_permission"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_d1555adb93eb57ec97f002a84d"`
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_cfcf09d300efc2b636f12399c9"`
    );
    await queryRunner.query(`DROP TABLE "company_features"`);
    await queryRunner.query(`DROP TABLE "message"`);
    await queryRunner.query(`DROP TABLE "error_log"`);
    await queryRunner.query(`DROP TABLE "approval"`);
    await queryRunner.query(`DROP TYPE "public"."approval_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."approval_feature_enum"`);
    await queryRunner.query(`DROP TABLE "employee"`);
    await queryRunner.query(`DROP TABLE "expense"`);
    await queryRunner.query(`DROP TYPE "public"."expense_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."expense_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."expense_assettype_enum"`);
    await queryRunner.query(`DROP TABLE "inventory"`);
    await queryRunner.query(`DROP TYPE "public"."inventory_type_enum"`);
    await queryRunner.query(`DROP TABLE "bill"`);
    await queryRunner.query(`DROP TYPE "public"."bill_paymenttype_enum"`);
    await queryRunner.query(`DROP TABLE "store"`);
    await queryRunner.query(`DROP TABLE "store_item_stock"`);
    await queryRunner.query(`DROP TABLE "item"`);
    await queryRunner.query(`DROP TYPE "public"."item_type_enum"`);
    await queryRunner.query(`DROP TABLE "unit"`);
    await queryRunner.query(`DROP TABLE "batch_stock"`);
    await queryRunner.query(`DROP TABLE "transfer"`);
    await queryRunner.query(`DROP TABLE "import"`);
    await queryRunner.query(`DROP TABLE "company"`);
    await queryRunner.query(`DROP TABLE "payment"`);
    await queryRunner.query(`DROP TYPE "public"."payment_billingcycle_enum"`);
    await queryRunner.query(`DROP TYPE "public"."payment_status_enum"`);
    await queryRunner.query(`DROP TABLE "sync_history"`);
    await queryRunner.query(`DROP TABLE "department"`);
    await queryRunner.query(`DROP TABLE "user"`);
    await queryRunner.query(`DROP TYPE "public"."user_gender_enum"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_1759b664f9fdf5788e2b0af044"`
    );
    await queryRunner.query(`DROP TABLE "category"`);
    await queryRunner.query(`DROP TABLE "type"`);
    await queryRunner.query(`DROP TABLE "role"`);
    await queryRunner.query(`DROP TABLE "permission"`);
    await queryRunner.query(`DROP TABLE "feature"`);
    await queryRunner.query(`DROP TABLE "audit_base_entity"`);
    try {
      // Remove the admin user
      await queryRunner.query(`
                DELETE FROM "user" WHERE "email" = '<EMAIL>'
            `);
      console.log("Admin user removed");

      // Remove the admin role
      await queryRunner.query(`
                DELETE FROM "role" WHERE "companyId" = 0 AND "name" = 'admin'
            `);
      console.log("Admin role removed");

      // Remove the company
      await queryRunner.query(`
                DELETE FROM "company" WHERE "id" = 0
            `);
      console.log("Default company removed");
    } catch (error) {
      console.error("Rollback migration failed:", error.message);
      // Re-throw the error to indicate rollback failure
      throw error;
    }
  }
}
