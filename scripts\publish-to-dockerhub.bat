@echo off
setlocal enabledelayedexpansion

REM Check if logged in to Docker Hub
echo Checking Docker Hub login status...
docker info | findstr "Username: samxtu" >nul
if errorlevel 1 (
  echo Not logged in to Docker Hub. Please login:
  docker login -u samxtu
)

REM Build Docker image with multi-stage build
echo Building Docker image with multi-stage build...
docker build -t samxtu/talisia-api:latest -f Dockerfile.pkg .

REM Optional: Tag with version
if not "%~1"=="" (
  set VERSION=%~1
  echo Tagging image with version: !VERSION!
  docker tag samxtu/talisia-api:latest samxtu/talisia-api:!VERSION!
  
  REM Push the versioned tag
  echo Pushing versioned image to Docker Hub...
  docker push samxtu/talisia-api:!VERSION!
)

REM Push the latest tag
echo Pushing latest image to Docker Hub...
docker push samxtu/talisia-api:latest

echo ✅ Successfully published samxtu/talisia-api to Docker Hub!
echo    - Latest tag: samxtu/talisia-api:latest
if not "%~1"=="" (
  echo    - Version tag: samxtu/talisia-api:!VERSION!
)


