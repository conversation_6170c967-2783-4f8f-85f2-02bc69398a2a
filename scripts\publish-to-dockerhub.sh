#!/bin/bash
set -e

# Check if logged in to Docker Hub
echo "Checking Docker Hub login status..."
if ! docker info | grep -q "Username: samxtu"; then
  echo "Not logged in to Docker Hub. Please login:"
  docker login -u samxtu
fi

# Build TypeScript app
echo "Building TypeScript application..."
yarn build

# Create binary with pkg
echo "Creating executable binary with pkg..."
yarn pkg

# Build Docker image with the binary
echo "Building Docker image with the binary..."
docker build -t samxtu/talisia-api:latest -f Dockerfile.pkg .

# Optional: Tag with version
if [ ! -z "$1" ]; then
  VERSION=$1
  echo "Tagging image with version: $VERSION"
  docker tag samxtu/talisia-api:latest samxtu/talisia-api:$VERSION
  
  # Push the versioned tag
  echo "Pushing versioned image to Docker Hub..."
  docker push samxtu/talisia-api:$VERSION
fi

# Push the latest tag
echo "Pushing latest image to Docker Hub..."
docker push samxtu/talisia-api:latest

echo "✅ Successfully published samxtu/talisia-api to Docker Hub!"
echo "   - Latest tag: samxtu/talisia-api:latest"
if [ ! -z "$1" ]; then
  echo "   - Version tag: samxtu/talisia-api:$VERSION"
fi