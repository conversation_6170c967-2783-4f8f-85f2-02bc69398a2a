import { MyContext } from "../types";
import { MiddlewareFn } from "type-graphql";
import { verifyToken } from "../utils/jwt";

export const isAuth: MiddlewareFn<MyContext> = async ({ context }, next) => {
  // Check session authentication
  if (context.req.session.userId) {
    return next();
  }

  // Check JWT authentication
  const authHeader = context.req.headers.authorization;
  if (authHeader) {
    const token = authHeader.split(" ")[1];
    const payload = verifyToken(token);
    if (payload) {
      // Attach the token payload to the request for later use
      context.req.session.userId = payload.userId;
      context.req.session.companyId = payload.companyId;
      context.req.session.role = payload.role;
      return next();
    }
  }

  throw new Error("Not authenticated");
};
