import { Company } from "../entities/Company";
// import { MyContext } from "../types";
import {
  Resolver,
  Mutation,
  Query,
  Arg,
  InputType,
  Field,
  UseMiddleware,
  ObjectType,
  Ctx,
} from "type-graphql";
import { isAuth } from "../middleware/isAuth";
import { BooleanResponse, FieldError } from "./user";
import { MyContext } from "../types";
import { logError } from "../utils/utils";
import { getConnection } from "typeorm";

@ObjectType()
export class BooleanResponseId {
  @Field({ nullable: true })
  id?: number;
  @Field(() => String, { nullable: true })
  name?: string;
  @Field()
  status: boolean;
  @Field(() => FieldError, { nullable: true })
  error?: FieldError;
}

@InputType()
class RegisterCompanyAddressedArgs {
  @Field()
  name: string;
  @Field()
  tinNumber: string;
  @Field()
  registrationNumber: string;
  @Field()
  type: string;
  @Field()
  city: string;
  @Field()
  district: string;
  @Field()
  ward: string;
  @Field()
  street: string;
}

@InputType()
class RegisterCompanyArgs {
  @Field()
  name: string;
  @Field()
  tinNumber: string;
  @Field()
  registrationNumber: string;
  @Field()
  type: string;
  @Field({ nullable: true })
  location: string;
}

@Resolver(Company)
export class CompanyResolver {
  @Mutation(() => BooleanResponseId)
  async registerCompany(
    @Arg("params") params: RegisterCompanyArgs
  ): Promise<BooleanResponseId> {
    let company: Company;

    try {
      // Using a transaction to ensure atomicity
      company = await getConnection().transaction(
        async (transactionalEntityManager) => {
          // Create the company
          const newCompany = await transactionalEntityManager.create(Company, {
            name: params.name,
            tinNumber: params.tinNumber,
            registrationNumber: params.registrationNumber,
            type: params.type,
            location: params.location,
          });

          // Save the company first
          const savedCompany = await transactionalEntityManager.save(
            newCompany
          );

          // Set companyId and save again
          savedCompany.companyId = savedCompany.id;
          return await transactionalEntityManager.save(savedCompany);
        }
      );
      return { status: true, id: company.id, name: company.name };
    } catch (err) {
      if (err.code === "23505") {
        await logError(
          0,
          "Company name already exists",
          "COMPANY_REGISTER_DUPLICATE",
          JSON.stringify(err),
          "medium",
          `Company registration failed - duplicate name: ${params.name}`
        );
        return {
          status: false,
          error: {
            target: "companyname",
            message: "companyname already taken!",
          },
        };
      }

      await logError(
        0,
        err.message,
        "COMPANY_REGISTER_ERROR",
        JSON.stringify(err),
        "high",
        `Company registration failed: ${params.name}`
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Something went wrong, try again!",
        },
      };
    }
  }

  @Mutation(() => BooleanResponse)
  async addCompanyWithAddress(
    @Arg("params") params: RegisterCompanyAddressedArgs
  ): Promise<BooleanResponse> {
    let company: Company;
    try {
      company = await Company.create(params).save();
      company.companyId = company.id;
      company.location = `${params.city}, ${params.district}, ${params.ward}, ${params.street}`;
      await company.save();
      return { status: true };
    } catch (err) {
      if (err.code === "23505") {
        await logError(
          company!.id,
          "Company name already exists",
          "COMPANY_ADD_WITH_ADDRESS_DUPLICATE",
          JSON.stringify(err),
          "medium",
          `Company registration with address failed - duplicate name: ${params.name}`
        );
        return {
          status: false,
          error: {
            target: "companyname",
            message: "companyname already taken!",
          },
        };
      }

      await logError(
        company!.id,
        err.message,
        "COMPANY_ADD_WITH_ADDRESS_ERROR",
        JSON.stringify(err),
        "high",
        `Company registration with address failed: ${params.name}`
      );
      return {
        status: false,
        error: {
          target: "general",
          message: "Something went wrong, try again!",
        },
      };
    }
  }

  @Query(() => Company, { nullable: true })
  @UseMiddleware(isAuth)
  async getCompany(
    @Arg("id") id: number,
    @Ctx() { req }: MyContext
  ): Promise<Company | undefined> {
    // Only allow access to own company
    if (id !== req.session.companyId && req.session.role !== "admin") {
      return undefined;
    }
    return Company.findOne(id, {
      relations: ["employees", "employees.role", "features", "payments"],
    });
  }

  @Query(() => [Company])
  @UseMiddleware(isAuth)
  async getCompanies(): Promise<Company[]> {
    return Company.find({
      relations: ["employees", "employees.role", "payments"],
    });
  }
}
