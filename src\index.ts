import "reflect-metadata";
import express, { Request, Response, NextFunction } from "express";
import { ApolloServer } from "apollo-server-express";
import { buildSchema } from "type-graphql";
import Redis from "ioredis";
import session from "express-session";
import connectRedis from "connect-redis";
import cors from "cors";
import path from "path";
import { createConnection } from "typeorm";
import dotenv from "dotenv";
import { COOKIE_NAME, customSecret } from "./constants.js";
import { RoleResolver } from "./resolvers/role";
import { PermissionResolver } from "./resolvers/permission";
import { FeatureResolver } from "./resolvers/feature";
import { CategoryResolver } from "./resolvers/category";
import { TypeResolver } from "./resolvers/type";
import { CompanyResolver } from "./resolvers/company";
import { InventoryResolver } from "./resolvers/inventory";
import { ExpenseResolver } from "./resolvers/Expense";
import { BillResolver } from "./resolvers/billing";
import scheduler from "./utils/scheduler.js";
import { DepartmentResolver } from "./resolvers/department.js";
import { SyncResolver } from "./resolvers/sync.js";
import { verifyToken } from "./utils/jwt";
import { MessageResolver } from "./resolvers/message";
import { LogResolver } from "./resolvers/log.js";
import { UserResolver } from "./resolvers/user.js";
import { PaymentResolver } from "./resolvers/Payment.js";

// Load environment variables
dotenv.config();

const main = async () => {
  try {
    // Database connection
    const conn = await createConnection({
      type: "postgres",
      host: "postgres", // Docker Compose service name
      port: 5432,
      database: `${process.env.POSTGRES_DB}`,
      username: `${process.env.POSTGRES_USER}`,
      password: `${process.env.POSTGRES_PASSWORD}`,
      logging: true,
      synchronize: false, // Set to false in production to avoid accidental schema changes
      entities: [
        path.join(__dirname, "./entities/*"),
        path.join(__dirname, "./entities/Inventory/*"),
        path.join(__dirname, "./entities/Patient/*"),
      ],
      migrations: [path.join(__dirname, "./migrations/*")],
      cli: {
        migrationsDir: "./migrations",
      },
    });

    if (!conn.isConnected) {
      console.error("Failed to connect to the database");
      return;
    }

    console.log("Connected to db:", conn.name);

    // Automatically run pending migrations
    await conn.runMigrations();

    // Express app
    const desktopApp = express();

    // Redis setup
    const RedisStore = connectRedis(session as any);
    const redis = new Redis({
      host: "redis",
      port: parseInt("6379"),
    });

    desktopApp.set("trust proxy", 1);

    // Session middleware
    desktopApp.use(
      (session as any)({
        name: COOKIE_NAME,
        store: new (RedisStore as any)({ client: redis }),
        cookie: {
          maxAge: 1000 * 60 * 60 * 24 * 365 * 10,
          httpOnly: true,
          sameSite: "none",
          secure: true,
        },
        secret: customSecret!,
        resave: false,
        saveUninitialized: true,
      })
    );

    // Load allowed origins from environment variable or fallback to defaults
    const allowedOrigins = [
      "https://talisia-pos.netlify.app",
      "https://localhost:5123",
    ];

    // Custom middleware to bypass CORS for mobile apps with X-Mobile-App header
    desktopApp.use((req: Request, res: Response, next: NextFunction) => {
      if (req.get("X-Mobile-App") === "talisia") {
        // Allow mobile app requests without enforcing CORS origin
        res.header("Access-Control-Allow-Origin", req.headers.origin || "*");
        res.header("Access-Control-Allow-Credentials", "true");
        res.header("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        res.header(
          "Access-Control-Allow-Headers",
          "Content-Type, Authorization, X-Mobile-App"
        );
        if (req.method === "OPTIONS") {
          res.sendStatus(200); // Handle preflight requests
          return;
        }
      }
      next();
    });

    // CORS middleware for web clients
    desktopApp.use(
      cors({
        origin: (origin, callback) => {
          // Allow requests with no origin (e.g., mobile apps, Postman)
          if (!origin) {
            return callback(null, true);
          }
          // Allow Expo dev server
          if (origin.includes(":5823") || origin.includes(":8081")) {
            return callback(null, true);
          }
          // Allow specific web origins
          if (allowedOrigins.includes(origin)) {
            return callback(null, true);
          }
          return callback(new Error(`Origin ${origin} not allowed by CORS`));
        },
        credentials: true,
        methods: ["GET", "POST", "OPTIONS"],
        allowedHeaders: ["Content-Type", "Authorization", "X-Mobile-App"],
      })
    );

    // Apollo Server setup
    const apolloDesktopServer = new ApolloServer({
      schema: await buildSchema({
        resolvers: [
          LogResolver,
          UserResolver,
          RoleResolver,
          PermissionResolver,
          CategoryResolver,
          TypeResolver,
          CompanyResolver,
          FeatureResolver,
          InventoryResolver,
          ExpenseResolver,
          BillResolver,
          DepartmentResolver,
          SyncResolver,
          MessageResolver,
          PaymentResolver,
        ],
        validate: true,
        emitSchemaFile: true,
      }),
      context: ({ req, res }) => {
        // Extract JWT from Authorization header
        const token = req.headers.authorization?.split(" ")[1];
        if (token) {
          const payload = verifyToken(token);
          if (payload) {
            req.session!.userId = payload.userId;
            req.session!.companyId = payload.companyId;
            req.session!.role = payload.role;
          }
        }
        return { req, res, redis, conn };
      },
    });

    apolloDesktopServer.applyMiddleware({
      app: desktopApp as any,
      cors: false, // CORS handled by Express middleware
    });

    // Start scheduler for cron jobs
    scheduler();

    // Create both HTTP and HTTPS servers
    desktopApp.listen(5823, () => {
      console.log(`HTTP Server running on http://localhost:${5823}`);
    });
  } catch (error) {
    console.error("Error in main function:", error);
  }
};

main();
