# Build stage
FROM node:20-slim AS builder

WORKDIR /usr/src/app

# Install build dependencies
RUN apt-get update && apt-get install -y build-essential python3

# Copy package files
COPY package.json yarn.lock ./

# Install dependencies
RUN yarn install --frozen-lockfile

# Copy source code
COPY . .

# Build TypeScript
RUN yarn build

# Install pkg globally and create binary
RUN yarn global add pkg && \
    pkg . --targets node16-linux-x64 --out-path=bin

# Runtime stage
FROM node:20-slim

WORKDIR /usr/app

# Copy binary
COPY --from=builder /usr/src/app/bin/talisia-api ./app

# Copy necessary files
COPY --from=builder /usr/src/app/.env.example ./.env
COPY --from=builder /usr/src/app/certificates ./certificates

# Install runtime dependencies
RUN apt-get update && apt-get install -y ca-certificates wget && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r appgroup && useradd -r -g appgroup appuser

# Set proper permissions
RUN chown -R appuser:appgroup /usr/app
RUN chmod +x /usr/app/app

# Switch to non-root user
USER appuser

# Set environment
ENV NODE_ENV=production

EXPOSE 5823 443

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:5823/health || exit 1

# Run the binary
CMD ["./app"]




